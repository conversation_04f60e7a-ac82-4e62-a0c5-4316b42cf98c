kubectl apply -f cluster-autoscaler-rbac.yaml
kubectl apply -f autoscaler.yaml


kubectl logs <pods> -n kube-system --tail=50


kubectl get pods -n kube-system | grep cluster-autoscaler

kubectl delete pod cluster-autoscaler-56464cc7db-c7pdr -n kube-system

kubectl get pods | grep Pending


kubectl logs -f cluster-autoscaler-56464cc7db-59svp -n kube-system

kubectl delete pod -n kube-system -l app=cluster-autoscaler


 kubectl get all -n kube-system | grep cluster-autoscaler




 C:\Users\<USER>\Documents>kubectl apply -f auto-rbac.yaml.txt
clusterrole.rbac.authorization.k8s.io/cluster-autoscaler configured
serviceaccount/cluster-autoscaler unchanged
clusterrolebinding.rbac.authorization.k8s.io/cluster-autoscaler unchanged

C:\Users\<USER>\Documents>kubectl apply -f auto.yaml.txt
serviceaccount/cluster-autoscaler unchanged
clusterrole.rbac.authorization.k8s.io/cluster-autoscaler configured
deployment.apps/cluster-autoscaler created

C:\Users\<USER>\Documents>
