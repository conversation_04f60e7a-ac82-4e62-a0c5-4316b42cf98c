#!/usr/bin/env python3

import boto3
import sys
from datetime import datetime, timedelta
from concurrent.futures import ThreadPoolExecutor, as_completed
import time

class GlacierRestoreManager:
    def __init__(self, aws_access_key_id, aws_secret_access_key, aws_session_token, region='us-east-1'):
        self.aws_access_key_id = aws_access_key_id
        self.aws_secret_access_key = aws_secret_access_key
        self.aws_session_token = aws_session_token
        self.region = region
        self.bucket_name = "tsc-cloudwatch-logs-archive"
        self.base_prefix = "cloudwatch-logs/2025/06/"
        
        # Initialize AWS session with credentials
        try:
            self.session = boto3.Session(
                aws_access_key_id=aws_access_key_id,
                aws_secret_access_key=aws_secret_access_key,
                aws_session_token=aws_session_token,
                region_name=region
            )
            self.s3_client = self.session.client('s3')
            print(f"✓ Successfully initialized AWS session with provided credentials")
        except Exception as e:
            print(f"✗ Failed to initialize AWS session: {str(e)}")
            sys.exit(1)
    
    def test_credentials(self):
        """Test AWS credentials by calling STS get-caller-identity"""
        try:
            sts_client = self.session.client('sts')
            response = sts_client.get_caller_identity()
            print(f"✓ Credentials valid for: {response.get('Arn', 'Unknown')}")
            return True
        except Exception as e:
            print(f"✗ Invalid credentials: {str(e)}")
            return False
    
    def list_objects_from_june_25(self):
        """List all objects from June 25th ONLY"""
        all_objects = []
        
        print("Scanning for objects from June 25th ONLY...")
        
        # June 25th only (scan all hours 00-23)
        day = 25
        day_prefix = f"{self.base_prefix}{day:02d}/"
        print(f"  Scanning day: {day_prefix}")
        
        # Scan all hours (00-23) for June 25th
        for hour in range(24):
            hour_prefix = f"{day_prefix}{hour:02d}/"
            objects = self._list_objects_with_prefix(hour_prefix)
            all_objects.extend(objects)
            if objects:
                print(f"    Hour {hour:02d}: Found {len(objects)} objects")
        
        print(f"\n📊 Total objects found: {len(all_objects)}")
        return all_objects
    
    def _list_objects_with_prefix(self, prefix):
        """List objects with specific prefix"""
        objects = []
        try:
            paginator = self.s3_client.get_paginator('list_objects_v2')
            pages = paginator.paginate(Bucket=self.bucket_name, Prefix=prefix)
            
            for page in pages:
                if 'Contents' in page:
                    for obj in page['Contents']:
                        objects.append({
                            'Key': obj['Key'],
                            'StorageClass': obj.get('StorageClass', 'STANDARD'),
                            'Size': obj.get('Size', 0),
                            'LastModified': obj.get('LastModified')
                        })
        except Exception as e:
            print(f"    ✗ Error listing objects with prefix {prefix}: {str(e)}")
        
        return objects
    
    def filter_glacier_objects(self, objects):
        """Filter objects that are in Glacier storage classes"""
        glacier_objects = []
        deep_archive_objects = []
        standard_objects = []
        
        for obj in objects:
            storage_class = obj['StorageClass']
            if storage_class == 'GLACIER':
                glacier_objects.append(obj)
            elif storage_class == 'DEEP_ARCHIVE':
                deep_archive_objects.append(obj)
            else:
                standard_objects.append(obj)
        
        print(f"\n📈 Storage Class Summary:")
        print(f"  GLACIER objects: {len(glacier_objects)}")
        print(f"  DEEP_ARCHIVE objects: {len(deep_archive_objects)}")
        print(f"  STANDARD/IA objects: {len(standard_objects)}")
        
        # Combine glacier and deep archive for restoration
        all_glacier_objects = glacier_objects + deep_archive_objects
        
        return all_glacier_objects, standard_objects
    
    def restore_single_object(self, obj_key, tier='Standard', days=30):
        """Restore a single object from Glacier"""
        try:
            response = self.s3_client.restore_object(
                Bucket=self.bucket_name,
                Key=obj_key,
                RestoreRequest={
                    'Days': days,
                    'GlacierJobParameters': {
                        'Tier': tier
                    }
                }
            )
            return {'key': obj_key, 'status': 'success', 'message': 'Restore initiated'}
        
        except self.s3_client.exceptions.ClientError as e:
            error_code = e.response['Error']['Code']
            if error_code == 'RestoreAlreadyInProgress':
                return {'key': obj_key, 'status': 'already_in_progress', 'message': 'Restore already in progress'}
            elif error_code == 'InvalidObjectState':
                return {'key': obj_key, 'status': 'not_needed', 'message': 'Object already restored or not in Glacier'}
            else:
                return {'key': obj_key, 'status': 'error', 'message': str(e)}
        except Exception as e:
            return {'key': obj_key, 'status': 'error', 'message': str(e)}
    
    def restore_objects_parallel(self, glacier_objects, max_workers=5, tier='Standard', days=7):
        """Restore objects in parallel (using Standard tier for Deep Archive)"""
        print(f"\n🔄 Starting parallel restore with {max_workers} workers...")
        print(f"   Tier: {tier} (Deep Archive compatible)")
        print(f"   Days: {days}")
        print(f"   ⏰ Standard restoration: 12+ hours for Deep Archive")
        
        results = {
            'success': 0,
            'already_in_progress': 0,
            'not_needed': 0,
            'error': 0,
            'errors': []
        }
        
        with ThreadPoolExecutor(max_workers=max_workers) as executor:
            # Submit all restore tasks
            future_to_key = {
                executor.submit(self.restore_single_object, obj['Key'], tier, days): obj['Key'] 
                for obj in glacier_objects
            }
            
            # Process results as they complete
            for future in as_completed(future_to_key):
                result = future.result()
                status = result['status']
                key = result['key']
                
                results[status] += 1
                
                if status == 'success':
                    print(f"  ✓ {key}")
                elif status == 'already_in_progress':
                    print(f"  ⏳ {key} (already in progress)")
                elif status == 'not_needed':
                    print(f"  ℹ️ {key} (not needed)")
                else:
                    print(f"  ✗ {key}: {result['message']}")
                    results['errors'].append(f"{key}: {result['message']}")
                
                # Small delay to avoid overwhelming the API
                time.sleep(0.2)
        
        return results
    
    def check_restore_status(self, objects_to_check):
        """Check restore status of objects"""
        print(f"\n🔍 Checking restore status for {len(objects_to_check)} objects...")
        
        restored_objects = []
        still_restoring = []
        
        for obj in objects_to_check:
            try:
                response = self.s3_client.head_object(
                    Bucket=self.bucket_name,
                    Key=obj['Key']
                )
                
                restore_status = response.get('Restore')
                if restore_status:
                    if 'ongoing-request="false"' in restore_status:
                        restored_objects.append(obj)
                        print(f"  ✅ {obj['Key']} - RESTORED")
                    else:
                        still_restoring.append(obj)
                        print(f"  ⏳ {obj['Key']} - RESTORING")
                else:
                    # Object might not be in Glacier or already accessible
                    restored_objects.append(obj)
                    print(f"  ✅ {obj['Key']} - ACCESSIBLE")
                    
            except Exception as e:
                print(f"  ✗ {obj['Key']} - ERROR: {str(e)}")
                still_restoring.append(obj)
        
        print(f"\n📊 Restore Status Summary:")
        print(f"  Restored/Accessible: {len(restored_objects)}")
        print(f"  Still Restoring: {len(still_restoring)}")
        
        return restored_objects, still_restoring

def get_aws_credentials():
    """Get AWS credentials from user input"""
    print("\n🔐 Please enter your AWS credentials:")
    print("─" * 40)
    
    aws_access_key_id = input("AWS Access Key ID: ").strip()
    if not aws_access_key_id:
        print("❌ Access Key ID cannot be empty")
        return None, None, None
    
    aws_secret_access_key = input("AWS Secret Access Key: ").strip()
    if not aws_secret_access_key:
        print("❌ Secret Access Key cannot be empty")
        return None, None, None
    
    aws_session_token = input("AWS Session Token: ").strip()
    if not aws_session_token:
        print("❌ Session Token cannot be empty")
        return None, None, None
    
    return aws_access_key_id, aws_secret_access_key, aws_session_token

def main():
    print("🚀 CloudWatch Logs Glacier Restore Tool")
    print("=" * 50)
    
    # Get AWS credentials from user
    aws_access_key_id, aws_secret_access_key, aws_session_token = get_aws_credentials()
    
    if not all([aws_access_key_id, aws_secret_access_key, aws_session_token]):
        print("❌ All credentials are required. Exiting.")
        sys.exit(1)
    
    # Initialize restore manager
    restore_manager = GlacierRestoreManager(
        aws_access_key_id, 
        aws_secret_access_key, 
        aws_session_token
    )
    
    # Test credentials
    if not restore_manager.test_credentials():
        print("❌ Failed to authenticate with provided credentials. Exiting.")
        sys.exit(1)
    
    # Test S3 connection
    try:
        restore_manager.s3_client.head_bucket(Bucket=restore_manager.bucket_name)
        print(f"✅ Successfully connected to bucket: {restore_manager.bucket_name}")
    except Exception as e:
        print(f"❌ Cannot access bucket {restore_manager.bucket_name}: {str(e)}")
        sys.exit(1)
    
    # List all objects from June 25th onwards
    all_objects = restore_manager.list_objects_from_june_25()
    
    if not all_objects:
        print("❌ No objects found matching the criteria")
        sys.exit(1)
    
    # Filter Glacier objects
    glacier_objects, standard_objects = restore_manager.filter_glacier_objects(all_objects)
    
    if not glacier_objects:
        print("✅ All objects are already in Standard storage class")
        print("🎉 No restore needed!")
        return
    
    # Confirm restore
    print(f"\n⚠️  About to initiate STANDARD restore for {len(glacier_objects)} objects")
    print("   ⏰ Using Standard tier (12+ hours for Deep Archive) for 7 days")
    print(f"   💰 Estimated cost: ~${len(glacier_objects) * 0.01:.2f} (Deep Archive Standard rate)")
    
    confirm = input("\nProceed with Standard restore? (y/N): ").strip().lower()
    
    if confirm != 'y':
        print("❌ Restore cancelled")
        return
    
    # Start restore process
    restore_results = restore_manager.restore_objects_parallel(glacier_objects)
    
    # Print summary
    print(f"\n🎯 Restore Summary:")
    print(f"  ✅ Successfully initiated: {restore_results['success']}")
    print(f"  ⏳ Already in progress: {restore_results['already_in_progress']}")
    print(f"  ℹ️  Not needed: {restore_results['not_needed']}")
    print(f"  ❌ Errors: {restore_results['error']}")
    
    if restore_results['errors']:
        print(f"\n❌ Error Details:")
        for error in restore_results['errors']:
            print(f"  {error}")
    
    if restore_results['success'] > 0 or restore_results['already_in_progress'] > 0:
        print(f"\n⏰ Estimated completion time: 12+ hours (Deep Archive Standard)")
        print(f"📋 Objects will be available for 7 days after restore")
        print(f"💰 Total cost: ~${restore_results['success'] * 0.01:.2f}")
        print(f"\n💡 Check S3 console in 12+ hours to see restored objects!")

if __name__ == "__main__":
    main()