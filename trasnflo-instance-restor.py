#!/usr/bin/env python3
"""
EC2 Snapshot Restore Testing Script
Creates new EC2 instances from snapshots for quarterly testing
Leaves instances in stopped state for isolation before testing
"""

import boto3
import json
import time
import getpass
from typing import Dict, List, Optional

def get_aws_credentials():
    """Prompt user for AWS credentials"""
    print("AWS Authentication Required")
    print("=" * 30)
    
    access_key = input("Enter AWS Access Key ID: ").strip()
    secret_key = getpass.getpass("Enter AWS Secret Access Key: ").strip()
    
    # Check if session token is needed
    use_session_token = input("Do you need to provide a session token? (y/n): ").strip().lower()
    session_token = None
    
    if use_session_token in ['y', 'yes']:
        session_token = getpass.getpass("Enter AWS Session Token: ").strip()
    
    if not access_key or not secret_key:
        raise ValueError("Access Key ID and Secret Access Key are required")
    
    return access_key, secret_key, session_token

class EC2SnapshotRestoreManager:
    def __init__(self, region: str = 'us-east-1'):
        """Initialize the EC2 client with user-provided credentials"""
        print(f"Initializing AWS connection to region: {region}")
        
        # Get credentials from user
        access_key, secret_key, session_token = get_aws_credentials()
        
        # Create boto3 client with provided credentials
        if session_token:
            self.ec2_client = boto3.client(
                'ec2',
                region_name=region,
                aws_access_key_id=access_key,
                aws_secret_access_key=secret_key,
                aws_session_token=session_token
            )
        else:
            self.ec2_client = boto3.client(
                'ec2',
                region_name=region,
                aws_access_key_id=access_key,
                aws_secret_access_key=secret_key
            )
        
        self.region = region
        
        # Test the connection
        try:
            print("Testing AWS connection...")
            self.ec2_client.describe_regions(RegionNames=[region])
            print(f"✓ Successfully connected to AWS region {region}")
        except Exception as e:
            print(f"✗ Failed to connect to AWS: {str(e)}")
            raise
        
    def get_instance_details(self, instance_id: str) -> Dict:
        """Get detailed information about an existing instance"""
        try:
            response = self.ec2_client.describe_instances(InstanceIds=[instance_id])
            instance = response['Reservations'][0]['Instances'][0]
            return instance
        except Exception as e:
            print(f"Error getting instance details for {instance_id}: {str(e)}")
            return {}
    
    def create_ami_from_snapshot(self, snapshot_id: str, instance_name: str, 
                                original_instance: Dict) -> Optional[str]:
        """Create an AMI from a snapshot"""
        try:
            # Get original root device name
            root_device = original_instance.get('RootDeviceName', '/dev/sda1')
            
            # Create AMI from snapshot
            response = self.ec2_client.register_image(
                Name=f"{instance_name}-restore-{int(time.time())}",
                Description=f"Restore AMI for {instance_name} quarterly testing",
                Architecture=original_instance.get('Architecture', 'x86_64'),
                RootDeviceName=root_device,
                BlockDeviceMappings=[
                    {
                        'DeviceName': root_device,
                        'Ebs': {
                            'SnapshotId': snapshot_id,
                            'VolumeType': 'gp3',
                            'DeleteOnTermination': True
                        }
                    }
                ],
                VirtualizationType=original_instance.get('VirtualizationType', 'hvm'),
                EnaSupport=True  # Enable Enhanced Networking for t3 instance types
            )
            
            ami_id = response['ImageId']
            print(f"Created AMI {ami_id} from snapshot {snapshot_id}")
            
            # Wait for AMI to be available
            self._wait_for_ami_available(ami_id)
            return ami_id
            
        except Exception as e:
            print(f"Error creating AMI from snapshot {snapshot_id}: {str(e)}")
            return None
    
    def _wait_for_ami_available(self, ami_id: str, timeout: int = 600):
        """Wait for AMI to become available"""
        print(f"Waiting for AMI {ami_id} to become available...")
        waiter = self.ec2_client.get_waiter('image_available')
        try:
            waiter.wait(
                ImageIds=[ami_id],
                WaiterConfig={
                    'Delay': 15,
                    'MaxAttempts': timeout // 15
                }
            )
            print(f"AMI {ami_id} is now available")
        except Exception as e:
            print(f"Warning: Timeout waiting for AMI {ami_id}: {str(e)}")
    
    def create_restore_instance(self, original_instance: Dict, snapshot_id: str) -> Optional[str]:
        """Create a new instance from snapshot for restore testing"""
        try:
            instance_name = None
            for tag in original_instance.get('Tags', []):
                if tag['Key'] == 'Name':
                    instance_name = tag['Value']
                    break
            
            if not instance_name:
                print("Error: Could not find instance name in tags")
                return None
            
            # Create AMI from snapshot
            ami_id = self.create_ami_from_snapshot(snapshot_id, instance_name, original_instance)
            if not ami_id:
                return None
            
            # Prepare tags for new instance
            new_tags = []
            for tag in original_instance.get('Tags', []):
                if tag['Key'] == 'Name':
                    new_tags.append({
                        'Key': 'Name',
                        'Value': f"{tag['Value']}-restore"
                    })
                elif tag['Key'] not in ['aws:ec2launchtemplate:id', 'aws:ec2launchtemplate:version']:
                    new_tags.append(tag)
            
            # Add restore-specific tags
            new_tags.extend([
                {'Key': 'Purpose', 'Value': 'Quarterly-Restore-Testing'},
                {'Key': 'OriginalInstance', 'Value': original_instance['InstanceId']},
                {'Key': 'RestoreDate', 'Value': time.strftime('%Y-%m-%d')},
                {'Key': 'SourceSnapshot', 'Value': snapshot_id}
            ])
            
            # Extract networking details
            security_group_ids = [sg['GroupId'] for sg in original_instance.get('SecurityGroups', [])]
            subnet_id = original_instance.get('SubnetId')
            vpc_id = original_instance.get('VpcId')
            key_name = original_instance.get('KeyName')
            instance_type = original_instance.get('InstanceType')
            
            # Debug output - show what we're using
            print(f"  Instance Type: {instance_type}")
            print(f"  Key Name: {key_name}")
            print(f"  VPC ID: {vpc_id}")
            print(f"  Subnet ID: {subnet_id}")
            print(f"  Security Groups: {security_group_ids}")
            print(f"  EBS Optimized: {original_instance.get('EbsOptimized', False)}")
            print(f"  Availability Zone: {original_instance.get('Placement', {}).get('AvailabilityZone', 'N/A')}")
            
            # Check for multiple private IP addresses (like TFX02)
            network_interfaces = original_instance.get('NetworkInterfaces', [])
            if network_interfaces and len(network_interfaces[0].get('PrivateIpAddresses', [])) > 1:
                private_ips = [ip['PrivateIpAddress'] for ip in network_interfaces[0]['PrivateIpAddresses']]
                print(f"  Note: Original instance has {len(private_ips)} private IPs: {private_ips}")
                print(f"  Restore instance will only get the primary IP (new IP will be auto-assigned)")
            
            # Launch instance parameters
            launch_params = {
                'ImageId': ami_id,
                'MinCount': 1,
                'MaxCount': 1,
                'InstanceType': instance_type,
                'KeyName': key_name,
                'SecurityGroupIds': security_group_ids,
                'SubnetId': subnet_id,
                'TagSpecifications': [
                    {
                        'ResourceType': 'instance',
                        'Tags': new_tags
                    }
                ],
                'EbsOptimized': original_instance.get('EbsOptimized', False),
                'Monitoring': {
                    'Enabled': original_instance.get('Monitoring', {}).get('State') == 'enabled'
                },
                'DisableApiTermination': False,
                'InstanceInitiatedShutdownBehavior': 'stop'
            }
            
            # Add IAM instance profile if present
            if 'IamInstanceProfile' in original_instance:
                launch_params['IamInstanceProfile'] = {
                    'Arn': original_instance['IamInstanceProfile']['Arn']
                }
            
            # Add placement if specified
            if 'Placement' in original_instance:
                launch_params['Placement'] = {
                    'AvailabilityZone': original_instance['Placement']['AvailabilityZone']
                }
            
            # Launch the instance
            response = self.ec2_client.run_instances(**launch_params)
            new_instance_id = response['Instances'][0]['InstanceId']
            
            print(f"Launched new instance: {new_instance_id}")
            
            # Wait for instance to be running, then stop it
            self._wait_and_stop_instance(new_instance_id, f"{instance_name}-restore")
            
            return new_instance_id
            
        except Exception as e:
            print(f"Error creating restore instance: {str(e)}")
            return None
    
    def _wait_and_stop_instance(self, instance_id: str, instance_name: str):
        """Wait for instance to be running, then stop it"""
        try:
            print(f"Waiting for instance {instance_id} ({instance_name}) to be running...")
            waiter = self.ec2_client.get_waiter('instance_running')
            waiter.wait(
                InstanceIds=[instance_id],
                WaiterConfig={'Delay': 15, 'MaxAttempts': 20}
            )
            
            print(f"Instance {instance_id} is running, now stopping for isolation...")
            self.ec2_client.stop_instances(InstanceIds=[instance_id])
            
            print(f"Waiting for instance {instance_id} to stop...")
            waiter = self.ec2_client.get_waiter('instance_stopped')
            waiter.wait(
                InstanceIds=[instance_id],
                WaiterConfig={'Delay': 15, 'MaxAttempts': 20}
            )
            
            print(f"Instance {instance_id} ({instance_name}) is now stopped and ready for testing")
            
        except Exception as e:
            print(f"Error stopping instance {instance_id}: {str(e)}")

    def create_and_attach_data_volumes(self, instance_id: str, data_snapshots: list,
                                     original_instance: Dict) -> bool:
        """Create and attach additional data volumes from snapshots"""
        try:
            print(f"Creating and attaching {len(data_snapshots)} data volume(s)...")

            # Get original instance's block device mappings for device names
            original_volumes = {}
            for bdm in original_instance.get('BlockDeviceMappings', []):
                if 'Ebs' in bdm:
                    original_volumes[bdm['DeviceName']] = bdm['Ebs']['VolumeId']

            # Get volume details for each original volume to match with snapshots
            volume_details = {}
            for device, volume_id in original_volumes.items():
                try:
                    response = self.ec2_client.describe_volumes(VolumeIds=[volume_id])
                    volume_details[device] = response['Volumes'][0]
                except Exception as e:
                    print(f"Warning: Could not get details for volume {volume_id}: {e}")

            # Create and attach each data volume
            attached_volumes = []
            device_letters = ['b', 'c', 'd', 'e', 'f', 'g', 'h']  # Start from /dev/sdb

            for i, snapshot_id in enumerate(data_snapshots):
                try:
                    # Find matching device name from original instance
                    device_name = f"/dev/sd{device_letters[i]}"

                    # Get snapshot details
                    snap_response = self.ec2_client.describe_snapshots(SnapshotIds=[snapshot_id])
                    snapshot = snap_response['Snapshots'][0]

                    print(f"  Creating volume from snapshot {snapshot_id} ({snapshot['VolumeSize']} GiB)...")

                    # Create volume from snapshot
                    volume_response = self.ec2_client.create_volume(
                        SnapshotId=snapshot_id,
                        AvailabilityZone=original_instance['Placement']['AvailabilityZone'],
                        VolumeType='gp3',
                        TagSpecifications=[
                            {
                                'ResourceType': 'volume',
                                'Tags': [
                                    {'Key': 'Name', 'Value': f"{original_instance.get('Tags', [{}])[0].get('Value', 'Unknown')}-restore-data-{i+1}"},
                                    {'Key': 'Purpose', 'Value': 'Quarterly-Restore-Testing'},
                                    {'Key': 'SourceSnapshot', 'Value': snapshot_id}
                                ]
                            }
                        ]
                    )

                    volume_id = volume_response['VolumeId']
                    print(f"  Created volume {volume_id}, waiting for it to be available...")

                    # Wait for volume to be available
                    waiter = self.ec2_client.get_waiter('volume_available')
                    waiter.wait(VolumeIds=[volume_id], WaiterConfig={'Delay': 5, 'MaxAttempts': 60})

                    print(f"  Attaching volume {volume_id} to {device_name}...")

                    # Attach volume to instance
                    self.ec2_client.attach_volume(
                        VolumeId=volume_id,
                        InstanceId=instance_id,
                        Device=device_name
                    )

                    attached_volumes.append({
                        'volume_id': volume_id,
                        'device': device_name,
                        'snapshot_id': snapshot_id,
                        'size': snapshot['VolumeSize']
                    })

                    print(f"  ✓ Successfully attached {snapshot['VolumeSize']} GiB volume {volume_id} to {device_name}")

                except Exception as e:
                    print(f"  ✗ Error creating/attaching volume from snapshot {snapshot_id}: {e}")
                    return False

            print(f"✓ Successfully attached {len(attached_volumes)} data volume(s)")
            return True

        except Exception as e:
            print(f"Error creating data volumes: {str(e)}")
            return False

def main():
    """Main function to create restore instances"""
    
    # Configuration: Instance ID -> Snapshot ID mapping
    # For multi-volume instances, use format: 'instance_id': {'root': 'snap-xxx', 'data': ['snap-yyy', 'snap-zzz']}
    # For single-volume instances, use format: 'instance_id': 'snap-xxx' (backward compatibility)
    RESTORE_CONFIG = {
        # 'i-04f58b766504a16b4': 'snap-0a1b5db2e8beaedce',  # TFX02 - Already created
        # 'i-02cc5b91a68bceb4f': 'snap-0dfdd9f20d538d2ff',  # TFM-MWS05 - Already created
        # 'i-00ad62412055bdf3f': 'snap-0f362ba041cbab493',  # DCT-RDR03 - Already created
        # 'i-0937166b7c4973e97': 'snap-0ad9ab39a53a669bb',  # DC0-TSCAN-GW04 - Already created
        'i-0a66c210dee1b7684': {  # DC1-STG-WEB01 (multi-volume)
            'root': 'snap-0dcf8a050a8a33035',  # 80 GiB root volume
            'data': ['snap-0ede7de25646795b1']  # 2000 GiB data volume
        },
         'i-0a725c4a3c2f02ece': 'snap-050ad7eaffc16bacd'  # Single volume instance (replace with actual snapshot)
    }
    
    print("Starting EC2 Snapshot Restore Testing Process")
    print("=" * 50)
    
    manager = EC2SnapshotRestoreManager()
    created_instances = []
    
    print(f"\nFound {len(RESTORE_CONFIG)} instances to process:")
    for instance_id, snapshot_id in RESTORE_CONFIG.items():
        print(f"  {instance_id} -> {snapshot_id}")
    
    for original_instance_id, snapshot_config in RESTORE_CONFIG.items():
        print(f"\n{'='*50}")
        print(f"Processing: {original_instance_id}")

        # Handle both single snapshot (string) and multi-volume (dict) configurations
        if isinstance(snapshot_config, str):
            # Single volume instance (backward compatibility)
            root_snapshot = snapshot_config
            data_snapshots = []
            print(f"Root snapshot: {root_snapshot}")
        elif isinstance(snapshot_config, dict):
            # Multi-volume instance
            root_snapshot = snapshot_config.get('root')
            data_snapshots = snapshot_config.get('data', [])
            print(f"Root snapshot: {root_snapshot}")
            if data_snapshots:
                print(f"Data snapshots: {data_snapshots}")
        else:
            print(f"Invalid configuration for {original_instance_id}")
            continue

        print("="*50)

        # Validate snapshot IDs (AWS snapshot IDs are typically 17-21 characters)
        if not root_snapshot or not root_snapshot.startswith('snap-') or len(root_snapshot) < 17:
            print(f"Invalid root snapshot ID: {root_snapshot}")
            continue

        for snap in data_snapshots:
            if not snap.startswith('snap-') or len(snap) < 17:
                print(f"Invalid data snapshot ID: {snap}")
                continue

        print("✓ Valid snapshot ID format(s)")

        # Get original instance details
        print(f"Fetching instance details for {original_instance_id}...")
        original_instance = manager.get_instance_details(original_instance_id)
        if not original_instance:
            print(f"Failed to get details for {original_instance_id}")
            continue

        print("✓ Successfully retrieved instance details")

        # Get instance name
        instance_name = "Unknown"
        for tag in original_instance.get('Tags', []):
            if tag['Key'] == 'Name':
                instance_name = tag['Value']
                break

        print(f"Instance name: {instance_name}")
        print(f"Creating restore instance for {instance_name}...")

        # Create restore instance from root snapshot
        new_instance_id = manager.create_restore_instance(original_instance, root_snapshot)

        if new_instance_id:
            # If there are data snapshots, create and attach data volumes
            data_volumes_success = True
            if data_snapshots:
                print(f"\nAttaching {len(data_snapshots)} additional data volume(s)...")
                data_volumes_success = manager.create_and_attach_data_volumes(
                    new_instance_id, data_snapshots, original_instance
                )

            if data_volumes_success:
                created_instances.append({
                    'original_name': instance_name,
                    'original_id': original_instance_id,
                    'restore_name': f"{instance_name}-restore",
                    'restore_id': new_instance_id,
                    'root_snapshot': root_snapshot,
                    'data_snapshots': data_snapshots
                })
                print(f"✓ Successfully created restore instance: {instance_name}-restore ({new_instance_id})")
            else:
                print(f"✗ Failed to attach data volumes for {instance_name}")
        else:
            print(f"✗ Failed to create restore instance for {instance_name}")
    
    # Summary
    print("\n" + "=" * 60)
    print("RESTORE TESTING INSTANCES SUMMARY")
    print("=" * 60)
    
    if created_instances:
        print("Successfully created instances:")
        for instance in created_instances:
            print(f"  • {instance['restore_name']} ({instance['restore_id']})")
            print(f"    Original: {instance['original_name']} ({instance['original_id']})")
            print(f"    Root snapshot: {instance['root_snapshot']}")
            if instance.get('data_snapshots'):
                print(f"    Data snapshots: {instance['data_snapshots']}")
            print()
        
        print("All instances are STOPPED and ready for isolated testing.")
        print("To start testing, manually start each instance through AWS Console or CLI.")
    else:
        print("No instances were created successfully.")

if __name__ == "__main__":
    main()