#!/usr/bin/env python3
"""
EC2 Snapshot Restore Testing Script
Creates new EC2 instances from snapshots for quarterly testing
Leaves instances in stopped state for isolation before testing
"""

import boto3
import json
import time
import getpass
from typing import Dict, List, Optional

def get_aws_credentials():
    """Prompt user for AWS credentials"""
    print("AWS Authentication Required")
    print("=" * 30)
    
    access_key = input("Enter AWS Access Key ID: ").strip()
    secret_key = getpass.getpass("Enter AWS Secret Access Key: ").strip()
    
    # Check if session token is needed
    use_session_token = input("Do you need to provide a session token? (y/n): ").strip().lower()
    session_token = None
    
    if use_session_token in ['y', 'yes']:
        session_token = getpass.getpass("Enter AWS Session Token: ").strip()
    
    if not access_key or not secret_key:
        raise ValueError("Access Key ID and Secret Access Key are required")
    
    return access_key, secret_key, session_token

class EC2SnapshotRestoreManager:
    def __init__(self, region: str = 'us-east-1'):
        """Initialize the EC2 client with user-provided credentials"""
        print(f"Initializing AWS connection to region: {region}")
        
        # Get credentials from user
        access_key, secret_key, session_token = get_aws_credentials()
        
        # Create boto3 client with provided credentials
        if session_token:
            self.ec2_client = boto3.client(
                'ec2',
                region_name=region,
                aws_access_key_id=access_key,
                aws_secret_access_key=secret_key,
                aws_session_token=session_token
            )
        else:
            self.ec2_client = boto3.client(
                'ec2',
                region_name=region,
                aws_access_key_id=access_key,
                aws_secret_access_key=secret_key
            )
        
        self.region = region
        
        # Test the connection
        try:
            print("Testing AWS connection...")
            self.ec2_client.describe_regions(RegionNames=[region])
            print(f"✓ Successfully connected to AWS region {region}")
        except Exception as e:
            print(f"✗ Failed to connect to AWS: {str(e)}")
            raise
        
    def get_instance_details(self, instance_id: str) -> Dict:
        """Get detailed information about an existing instance"""
        try:
            response = self.ec2_client.describe_instances(InstanceIds=[instance_id])
            instance = response['Reservations'][0]['Instances'][0]
            return instance
        except Exception as e:
            print(f"Error getting instance details for {instance_id}: {str(e)}")
            return {}
    
    def create_ami_from_snapshot(self, snapshot_id: str, instance_name: str, 
                                original_instance: Dict) -> Optional[str]:
        """Create an AMI from a snapshot"""
        try:
            # Get original root device name
            root_device = original_instance.get('RootDeviceName', '/dev/sda1')
            
            # Create AMI from snapshot
            response = self.ec2_client.register_image(
                Name=f"{instance_name}-restore-{int(time.time())}",
                Description=f"Restore AMI for {instance_name} quarterly testing",
                Architecture=original_instance.get('Architecture', 'x86_64'),
                RootDeviceName=root_device,
                BlockDeviceMappings=[
                    {
                        'DeviceName': root_device,
                        'Ebs': {
                            'SnapshotId': snapshot_id,
                            'VolumeType': 'gp3',
                            'DeleteOnTermination': True,
                            'Encrypted': False
                        }
                    }
                ],
                VirtualizationType=original_instance.get('VirtualizationType', 'hvm'),
                SriovNetSupport='simple' if original_instance.get('SriovNetSupport') else None
            )
            
            ami_id = response['ImageId']
            print(f"Created AMI {ami_id} from snapshot {snapshot_id}")
            
            # Wait for AMI to be available
            self._wait_for_ami_available(ami_id)
            return ami_id
            
        except Exception as e:
            print(f"Error creating AMI from snapshot {snapshot_id}: {str(e)}")
            return None
    
    def _wait_for_ami_available(self, ami_id: str, timeout: int = 600):
        """Wait for AMI to become available"""
        print(f"Waiting for AMI {ami_id} to become available...")
        waiter = self.ec2_client.get_waiter('image_available')
        try:
            waiter.wait(
                ImageIds=[ami_id],
                WaiterConfig={
                    'Delay': 15,
                    'MaxAttempts': timeout // 15
                }
            )
            print(f"AMI {ami_id} is now available")
        except Exception as e:
            print(f"Warning: Timeout waiting for AMI {ami_id}: {str(e)}")
    
    def create_restore_instance(self, original_instance: Dict, snapshot_id: str) -> Optional[str]:
        """Create a new instance from snapshot for restore testing"""
        try:
            instance_name = None
            for tag in original_instance.get('Tags', []):
                if tag['Key'] == 'Name':
                    instance_name = tag['Value']
                    break
            
            if not instance_name:
                print("Error: Could not find instance name in tags")
                return None
            
            # Create AMI from snapshot
            ami_id = self.create_ami_from_snapshot(snapshot_id, instance_name, original_instance)
            if not ami_id:
                return None
            
            # Prepare tags for new instance
            new_tags = []
            for tag in original_instance.get('Tags', []):
                if tag['Key'] == 'Name':
                    new_tags.append({
                        'Key': 'Name',
                        'Value': f"{tag['Value']}-restore"
                    })
                elif tag['Key'] not in ['aws:ec2launchtemplate:id', 'aws:ec2launchtemplate:version']:
                    new_tags.append(tag)
            
            # Add restore-specific tags
            new_tags.extend([
                {'Key': 'Purpose', 'Value': 'Quarterly-Restore-Testing'},
                {'Key': 'OriginalInstance', 'Value': original_instance['InstanceId']},
                {'Key': 'RestoreDate', 'Value': time.strftime('%Y-%m-%d')},
                {'Key': 'SourceSnapshot', 'Value': snapshot_id}
            ])
            
            # Extract networking details
            security_group_ids = [sg['GroupId'] for sg in original_instance.get('SecurityGroups', [])]
            subnet_id = original_instance.get('SubnetId')
            vpc_id = original_instance.get('VpcId')
            key_name = original_instance.get('KeyName')
            instance_type = original_instance.get('InstanceType')
            
            # Debug output - show what we're using
            print(f"  Instance Type: {instance_type}")
            print(f"  Key Name: {key_name}")
            print(f"  VPC ID: {vpc_id}")
            print(f"  Subnet ID: {subnet_id}")
            print(f"  Security Groups: {security_group_ids}")
            print(f"  EBS Optimized: {original_instance.get('EbsOptimized', False)}")
            print(f"  Availability Zone: {original_instance.get('Placement', {}).get('AvailabilityZone', 'N/A')}")
            
            # Check for multiple private IP addresses (like TFX02)
            network_interfaces = original_instance.get('NetworkInterfaces', [])
            if network_interfaces and len(network_interfaces[0].get('PrivateIpAddresses', [])) > 1:
                private_ips = [ip['PrivateIpAddress'] for ip in network_interfaces[0]['PrivateIpAddresses']]
                print(f"  Note: Original instance has {len(private_ips)} private IPs: {private_ips}")
                print(f"  Restore instance will only get the primary IP (new IP will be auto-assigned)")
            
            # Launch instance parameters
            launch_params = {
                'ImageId': ami_id,
                'MinCount': 1,
                'MaxCount': 1,
                'InstanceType': instance_type,
                'KeyName': key_name,
                'SecurityGroupIds': security_group_ids,
                'SubnetId': subnet_id,
                'TagSpecifications': [
                    {
                        'ResourceType': 'instance',
                        'Tags': new_tags
                    }
                ],
                'EbsOptimized': original_instance.get('EbsOptimized', False),
                'Monitoring': {
                    'Enabled': original_instance.get('Monitoring', {}).get('State') == 'enabled'
                },
                'DisableApiTermination': False,
                'InstanceInitiatedShutdownBehavior': 'stop'
            }
            
            # Add IAM instance profile if present
            if 'IamInstanceProfile' in original_instance:
                launch_params['IamInstanceProfile'] = {
                    'Arn': original_instance['IamInstanceProfile']['Arn']
                }
            
            # Add placement if specified
            if 'Placement' in original_instance:
                launch_params['Placement'] = {
                    'AvailabilityZone': original_instance['Placement']['AvailabilityZone']
                }
            
            # Launch the instance
            response = self.ec2_client.run_instances(**launch_params)
            new_instance_id = response['Instances'][0]['InstanceId']
            
            print(f"Launched new instance: {new_instance_id}")
            
            # Wait for instance to be running, then stop it
            self._wait_and_stop_instance(new_instance_id, f"{instance_name}-restore")
            
            return new_instance_id
            
        except Exception as e:
            print(f"Error creating restore instance: {str(e)}")
            return None
    
    def _wait_and_stop_instance(self, instance_id: str, instance_name: str):
        """Wait for instance to be running, then stop it"""
        try:
            print(f"Waiting for instance {instance_id} ({instance_name}) to be running...")
            waiter = self.ec2_client.get_waiter('instance_running')
            waiter.wait(
                InstanceIds=[instance_id],
                WaiterConfig={'Delay': 15, 'MaxAttempts': 20}
            )
            
            print(f"Instance {instance_id} is running, now stopping for isolation...")
            self.ec2_client.stop_instances(InstanceIds=[instance_id])
            
            print(f"Waiting for instance {instance_id} to stop...")
            waiter = self.ec2_client.get_waiter('instance_stopped')
            waiter.wait(
                InstanceIds=[instance_id],
                WaiterConfig={'Delay': 15, 'MaxAttempts': 20}
            )
            
            print(f"Instance {instance_id} ({instance_name}) is now stopped and ready for testing")
            
        except Exception as e:
            print(f"Error stopping instance {instance_id}: {str(e)}")

def main():
    """Main function to create restore instances"""
    
    # Configuration: Instance ID -> Snapshot ID mapping
    RESTORE_CONFIG = {
        'i-04f58b766504a16b4': 'snap-0a1b5db2e8beaedce',  # TFX02
        'i-02cc5b91a68bceb4f': 'snap-0dfdd9f20d538d2ff',  # TFM-MWS05
        'i-00ad62412055bdf3f': 'snap-0f362ba041cbab493',  # DCT-RDR03
        'i-0937166b7c4973e97': 'snap-0ad9ab39a53a669bb'   # DC0-TSCAN-GW04
    }
    
    print("Starting EC2 Snapshot Restore Testing Process")
    print("=" * 50)
    
    manager = EC2SnapshotRestoreManager()
    created_instances = []
    
    print(f"\nFound {len(RESTORE_CONFIG)} instances to process:")
    for instance_id, snapshot_id in RESTORE_CONFIG.items():
        print(f"  {instance_id} -> {snapshot_id}")
    
    for original_instance_id, snapshot_id in RESTORE_CONFIG.items():
        print(f"\n{'='*50}")
        print(f"Processing: {original_instance_id}")
        print(f"Snapshot: {snapshot_id}")
        print(f"{'='*50}")
        
        if not snapshot_id.startswith('snap-'):
            print(f"❌ Invalid snapshot ID format: {snapshot_id}")
            print(f"   Expected format: snap-xxxxxxxxxxxxxxx")
            continue
            
        print(f"✓ Valid snapshot ID format")
        
        # Get original instance details
        print(f"Fetching instance details for {original_instance_id}...")
        original_instance = manager.get_instance_details(original_instance_id)
        if not original_instance:
            print(f"❌ Failed to get details for {original_instance_id}")
            continue
        
        print(f"✓ Successfully retrieved instance details")
        
        # Get instance name
        instance_name = "Unknown"
        for tag in original_instance.get('Tags', []):
            if tag['Key'] == 'Name':
                instance_name = tag['Value']
                break
        
        print(f"Instance name: {instance_name}")
        
        # Create restore instance
        print(f"Creating restore instance for {instance_name}...")
        new_instance_id = manager.create_restore_instance(original_instance, snapshot_id)
        
        if new_instance_id:
            created_instances.append({
                'original_name': instance_name,
                'original_id': original_instance_id,
                'restore_name': f"{instance_name}-restore",
                'restore_id': new_instance_id,
                'snapshot_id': snapshot_id
            })
            print(f"✓ Successfully created restore instance: {instance_name}-restore ({new_instance_id})")
        else:
            print(f"✗ Failed to create restore instance for {instance_name}")
    
    # Summary
    print("\n" + "=" * 60)
    print("RESTORE TESTING INSTANCES SUMMARY")
    print("=" * 60)
    
    if created_instances:
        print("Successfully created instances:")
        for instance in created_instances:
            print(f"  • {instance['restore_name']} ({instance['restore_id']})")
            print(f"    Original: {instance['original_name']} ({instance['original_id']})")
            print(f"    Snapshot: {instance['snapshot_id']}")
            print()
        
        print("All instances are STOPPED and ready for isolated testing.")
        print("To start testing, manually start each instance through AWS Console or CLI.")
    else:
        print("No instances were created successfully.")

if __name__ == "__main__":
    main()