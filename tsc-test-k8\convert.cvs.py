import pandas as pd
import os
from datetime import datetime

def convert_timestamp(ts):
    # Convert the timestamp from the current format to the desired format
    dt = datetime.fromtimestamp(float(ts) / 1000.0)
    return dt.strftime('%Y-%m-%dT%H:%M:%S.000Z')

def process_file(file_path):
    # Read the CSV file
    df = pd.read_csv(file_path)

    # Convert the timestamps
    df['timestamp'] = df['timestamp'].apply(convert_timestamp)

    # Output to a new file
    new_file_path = file_path.replace('.csv', '_converted.csv')
    df.to_csv(new_file_path, index=False)
    return new_file_path

# File path
file_path = r"C:\Users\<USER>\OneDrive - Connectria LLC\Downloads\waf-log-events-viewer-result-2.csv"

# Process the file
new_file_path = process_file(file_path)
print(f"Converted file saved to: {new_file_path}")
