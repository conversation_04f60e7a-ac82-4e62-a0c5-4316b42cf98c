#!/usr/bin/env python3
"""
AWS IAM MFA Report Generator
This script identifies IAM users without MFA enabled and checks for access keys.
Outputs results to CSV format: {account_id}-iam-mfa-report.csv
"""

import boto3
import csv
from botocore.exceptions import ClientError, NoCredentialsError
import sys


def get_aws_credentials():
    """Prompt user for AWS credentials"""
    print("=== AWS Authentication ===")
    print("Please enter your AWS credentials:")
    print("Note: Keys will be visible as you type them")

    access_key = input("AWS Access Key ID: ").strip()
    secret_key = input("AWS Secret Access Key: ").strip()
    session_token = input("AWS Session Token (leave empty if not using temporary credentials): ").strip()

    if not session_token:
        session_token = None

    return access_key, secret_key, session_token


def create_aws_session(access_key, secret_key, session_token=None):
    """Create AWS session with provided credentials"""
    try:
        session = boto3.Session(
            aws_access_key_id=access_key,
            aws_secret_access_key=secret_key,
            aws_session_token=session_token
        )
        return session
    except Exception as e:
        print(f"Error creating AWS session: {e}")
        return None


def get_account_id(sts_client):
    """Get AWS account ID"""
    try:
        response = sts_client.get_caller_identity()
        return response['Account']
    except ClientError as e:
        print(f"Error getting account ID: {e}")
        return None


def check_user_mfa(iam_client, username):
    """Check if user has MFA devices enabled"""
    try:
        response = iam_client.list_mfa_devices(UserName=username)
        return len(response['MFADevices']) > 0
    except ClientError as e:
        print(f"Error checking MFA for user {username}: {e}")
        return False


def check_user_access_keys(iam_client, username):
    """Check if user has access keys"""
    try:
        response = iam_client.list_access_keys(UserName=username)
        return len(response['AccessKeyMetadata']) > 0
    except ClientError as e:
        print(f"Error checking access keys for user {username}: {e}")
        return False


def get_iam_users_without_mfa(session):
    """Get all IAM users without MFA enabled"""
    iam_client = session.client('iam')
    sts_client = session.client('sts')
    
    # Get account ID
    account_id = get_account_id(sts_client)
    if not account_id:
        return None, None
    
    users_without_mfa = []
    
    try:
        # Get all IAM users
        print("Fetching IAM users...")
        paginator = iam_client.get_paginator('list_users')
        
        for page in paginator.paginate():
            for user in page['Users']:
                username = user['UserName']
                print(f"Checking user: {username}")
                
                # Check if user has MFA enabled
                has_mfa = check_user_mfa(iam_client, username)
                
                # Only include users without MFA
                if not has_mfa:
                    has_access_keys = check_user_access_keys(iam_client, username)
                    
                    user_info = {
                        'UserName': username,
                        'UserId': user['UserId'],
                        'CreateDate': user['CreateDate'].strftime('%Y-%m-%d %H:%M:%S'),
                        'MFA': 'No',  # Only users without MFA
                        'Access_Keys': 'Yes' if has_access_keys else 'No'
                    }
                    users_without_mfa.append(user_info)
        
        return users_without_mfa, account_id
        
    except ClientError as e:
        print(f"Error fetching IAM users: {e}")
        return None, None
    except NoCredentialsError:
        print("AWS credentials not found or invalid")
        return None, None


def write_to_csv(users_data, account_id):
    """Write user data to CSV file"""
    filename = f"{account_id}-iam-mfa-report.csv"
    
    fieldnames = ['UserName', 'UserId', 'CreateDate', 'MFA', 'Access_Keys']
    
    try:
        with open(filename, 'w', newline='', encoding='utf-8') as csvfile:
            writer = csv.DictWriter(csvfile, fieldnames=fieldnames)
            writer.writeheader()
            writer.writerows(users_data)
        
        print(f"\nReport saved to: {filename}")
        return filename
    except Exception as e:
        print(f"Error writing to CSV: {e}")
        return None


def main():
    """Main function"""
    print("AWS IAM MFA Report Generator")
    print("=" * 40)
    
    # Get AWS credentials from user
    access_key, secret_key, session_token = get_aws_credentials()
    
    # Create AWS session
    print("\nAuthenticating with AWS...")
    session = create_aws_session(access_key, secret_key, session_token)
    
    if not session:
        print("Failed to create AWS session. Please check your credentials.")
        sys.exit(1)
    
    # Test authentication
    try:
        sts_client = session.client('sts')
        caller_identity = sts_client.get_caller_identity()
        print(f"Authentication successful!")
        print(f"Account ID: {caller_identity['Account']}")
        print(f"User ARN: {caller_identity['Arn']}")
    except Exception as e:
        print(f"Authentication failed: {e}")
        sys.exit(1)
    
    # Get users without MFA
    print("\nScanning IAM users...")
    users_without_mfa, account_id = get_iam_users_without_mfa(session)
    
    if users_without_mfa is None:
        print("Failed to retrieve IAM users")
        sys.exit(1)
    
    # Display results
    print(f"\nFound {len(users_without_mfa)} users without MFA enabled:")
    print("-" * 60)
    
    if users_without_mfa:
        for user in users_without_mfa:
            access_key_status = "Yes" if user['Access_Keys'] == 'Yes' else "No"
            print(f"User: {user['UserName']:<20} | Access Keys: {access_key_status}")
        
        # Write to CSV
        csv_file = write_to_csv(users_without_mfa, account_id)
        if csv_file:
            print(f"\nCSV report generated successfully: {csv_file}")
            print(f"Total users without MFA: {len(users_without_mfa)}")
        else:
            print("Failed to generate CSV report")
    else:
        print("Great! All users have MFA enabled.")
        # Still create an empty CSV for consistency
        write_to_csv([], account_id)


if __name__ == "__main__":
    main()