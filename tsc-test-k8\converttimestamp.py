import pandas as pd
from datetime import datetime, timedelta

# Define the path to the CSV file
input_file_path = r'C:\Users\<USER>\OneDrive - Connectria LLC\Downloads\log-events-viewer-result.csv'
output_file_path = r'C:\Users\<USER>\OneDrive - Connectria LLC\Downloads\converted_log_events.csv'

# Load the CSV file
df = pd.read_csv(input_file_path)

# Function to convert custom timestamp to datetime
def convert_timestamp(ts):
    minutes, seconds = map(float, ts.split(':'))
    total_seconds = minutes * 60 + seconds
    return timedelta(seconds=total_seconds)

# Apply the conversion to the 'timestamp' column
df['timestamp'] = df['timestamp'].apply(convert_timestamp)

# Assuming all times are on the same day; if not, add logic to handle dates
base_date = datetime(2024, 7, 1)  # Replace with the actual date if necessary
df['timestamp'] = df['timestamp'].apply(lambda x: base_date + x)

# Save the updated DataFrame to a new CSV file
df.to_csv(output_file_path, index=False)

print(f"Timestamps converted and saved to '{output_file_path}'")
