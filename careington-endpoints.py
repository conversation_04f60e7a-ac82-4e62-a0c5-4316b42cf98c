#!/usr/bin/env python3
"""
AWS EC2 and RDS Inventory Script
Collects EC2 instances and RDS information across all US regions
Exports data to organized CSV files
"""

import boto3
import csv
import sys
from datetime import datetime
from botocore.exceptions import ClientError, NoCredentialsError
import getpass

def get_aws_credentials():
    """Prompt user for AWS credentials with visible input"""
    print("=" * 60)
    print("AWS CREDENTIALS REQUIRED")
    print("=" * 60)
    
    print("\nPlease enter your AWS credentials:")
    access_key = input("AWS Access Key ID: ")
    print(f"Entered Access Key: {access_key}")
    
    secret_key = input("AWS Secret Access Key: ")
    print(f"Entered Secret Key: {secret_key}")
    
    session_token = input("AWS Session Token (leave empty if not using temporary credentials): ")
    if session_token:
        print(f"Entered Session Token: {session_token}")
    else:
        print("No session token provided")
        session_token = None
    
    print("\n" + "=" * 60)
    print("CREDENTIALS SUMMARY:")
    print("=" * 60)
    print(f"Access Key ID: {access_key}")
    print(f"Secret Access Key: {secret_key}")
    print(f"Session Token: {session_token if session_token else 'Not provided'}")
    print("=" * 60)
    
    confirm = input("\nProceed with these credentials? (y/n): ")
    if confirm.lower() != 'y':
        print("Exiting...")
        sys.exit(1)
    
    return access_key, secret_key, session_token

def get_us_regions():
    """Get all US regions"""
    us_regions = [
        'us-east-1',      # N. Virginia
        'us-east-2',      # Ohio
        'us-west-1',      # N. California
        'us-west-2',      # Oregon
    ]
    return us_regions

def get_ec2_instances(session, region):
    """Get EC2 instances for a specific region"""
    try:
        ec2 = session.client('ec2', region_name=region)
        response = ec2.describe_instances()
        
        instances = []
        for reservation in response['Reservations']:
            for instance in reservation['Instances']:
                # Skip terminated instances
                if instance['State']['Name'] == 'terminated':
                    continue
                
                # Get instance name from tags
                name = 'N/A'
                for tag in instance.get('Tags', []):
                    if tag['Key'] == 'Name':
                        name = tag['Value']
                        break
                
                # Get IP addresses
                private_ip = instance.get('PrivateIpAddress', 'N/A')
                public_ip = instance.get('PublicIpAddress', 'N/A')
                
                instances.append({
                    'Region': region,
                    'Name': name,
                    'Private IP': private_ip,
                    'Public IP': public_ip
                })
        
        return instances
    except ClientError as e:
        print(f"Error getting EC2 instances in {region}: {e}")
        return []

def get_rds_instances(session, region):
    """Get RDS instances and clusters for a specific region"""
    try:
        rds = session.client('rds', region_name=region)
        
        instances = []
        
        # Get RDS instances
        try:
            response = rds.describe_db_instances()
            for instance in response['DBInstances']:
                instances.append({
                    'Region': region,
                    'Name': instance['DBInstanceIdentifier'],
                    'Endpoint': instance.get('Endpoint', {}).get('Address', 'N/A')
                })
        except ClientError as e:
            print(f"Error getting RDS instances in {region}: {e}")
        
        # Get RDS clusters
        try:
            response = rds.describe_db_clusters()
            for cluster in response['DBClusters']:
                instances.append({
                    'Region': region,
                    'Name': cluster['DBClusterIdentifier'],
                    'Endpoint': cluster.get('Endpoint', 'N/A')
                })
        except ClientError as e:
            print(f"Error getting RDS clusters in {region}: {e}")
        
        return instances
    except ClientError as e:
        print(f"Error connecting to RDS in {region}: {e}")
        return []

def write_csv(data, filename, fieldnames):
    """Write data to CSV file"""
    if not data:
        print(f"No data to write for {filename}")
        return
    
    with open(filename, 'w', newline='', encoding='utf-8') as csvfile:
        writer = csv.DictWriter(csvfile, fieldnames=fieldnames)
        writer.writeheader()
        writer.writerows(data)
    
    print(f"✓ Exported {len(data)} records to {filename}")

def main():
    print("AWS EC2 and RDS Inventory Collection Tool")
    print("=" * 50)
    
    # Get AWS credentials
    access_key, secret_key, session_token = get_aws_credentials()
    
    # Create session
    try:
        if session_token:
            session = boto3.Session(
                aws_access_key_id=access_key,
                aws_secret_access_key=secret_key,
                aws_session_token=session_token
            )
        else:
            session = boto3.Session(
                aws_access_key_id=access_key,
                aws_secret_access_key=secret_key
            )
        
        # Test credentials
        sts = session.client('sts')
        identity = sts.get_caller_identity()
        print(f"\n✓ Credentials verified. Account: {identity['Account']}")
        print(f"✓ User ARN: {identity['Arn']}")
        
    except (NoCredentialsError, ClientError) as e:
        print(f"\n❌ Error with AWS credentials: {e}")
        sys.exit(1)
    
    # Get US regions
    regions = get_us_regions()
    print(f"\n🔍 Scanning {len(regions)} US regions: {', '.join(regions)}")
    
    all_ec2_instances = []
    all_rds_instances = []
    
    # Collect data from each region
    for region in regions:
        print(f"\n📍 Processing region: {region}")
        
        # Get EC2 instances
        print(f"  - Collecting EC2 instances...")
        ec2_instances = get_ec2_instances(session, region)
        all_ec2_instances.extend(ec2_instances)
        print(f"    Found {len(ec2_instances)} EC2 instances")
        
        # Get RDS instances
        print(f"  - Collecting RDS instances...")
        rds_instances = get_rds_instances(session, region)
        all_rds_instances.extend(rds_instances)
        print(f"    Found {len(rds_instances)} RDS instances/clusters")
    
    # Generate timestamp for filenames
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    
    # Write EC2 data to CSV
    print(f"\n📄 Exporting data...")
    if all_ec2_instances:
        # Sort by region, then by name
        all_ec2_instances.sort(key=lambda x: (x['Region'], x['Name']))
        
        ec2_filename = f"aws_ec2_inventory_{timestamp}.csv"
        ec2_fieldnames = ['Region', 'Name', 'Private IP', 'Public IP']
        write_csv(all_ec2_instances, ec2_filename, ec2_fieldnames)
    
    # Write RDS data to CSV
    if all_rds_instances:
        # Sort by region, then by name
        all_rds_instances.sort(key=lambda x: (x['Region'], x['Name']))
        
        rds_filename = f"aws_rds_inventory_{timestamp}.csv"
        rds_fieldnames = ['Region', 'Name', 'Endpoint']
        write_csv(all_rds_instances, rds_filename, rds_fieldnames)
    
    # Summary
    print(f"\n" + "=" * 50)
    print("COLLECTION SUMMARY")
    print("=" * 50)
    print(f"Total EC2 Instances: {len(all_ec2_instances)}")
    print(f"Total RDS Instances/Clusters: {len(all_rds_instances)}")
    
    # Region breakdown
    print(f"\nBy Region:")
    for region in regions:
        ec2_count = len([i for i in all_ec2_instances if i['Region'] == region])
        rds_count = len([i for i in all_rds_instances if i['Region'] == region])
        print(f"  {region}: {ec2_count} EC2, {rds_count} RDS")
    
    print(f"\n✅ Inventory collection complete!")
    print(f"Files generated:")
    if all_ec2_instances:
        print(f"  - {ec2_filename}")
    if all_rds_instances:
        print(f"  - {rds_filename}")

if __name__ == "__main__":
    main()