import boto3
from botocore.exceptions import NoCredentialsError, PartialCredentialsError

# Function to check if the record exists
def record_exists(route53, hosted_zone_id, name, record_type):
    try:
        response = route53.list_resource_record_sets(
            HostedZoneId=hosted_zone_id,
            StartRecordName=name,
            StartRecordType=record_type,
            MaxItems="1"
        )
        for record in response['ResourceRecordSets']:
            if record['Name'] == name and record['Type'] == record_type:
                return True
        return False
    except Exception as e:
        print(f"Error checking record {name}: {str(e)}")
        return False

# Function to split long DKIM strings
def split_dkim_string(dkim_string, max_length=255):
    return [dkim_string[i:i + max_length] for i in range(0, len(dkim_string), max_length)]

# Function to add the DNS record
def add_record(route53, hosted_zone_id, name, record_type, value):
    try:
        # Handle long DKIM records by splitting
        if record_type == "TXT" and len(value) > 255:
            value_parts = split_dkim_string(value)
            resource_records = [{'Value': f'"{part}"'} for part in value_parts]
        else:
            resource_records = [{'Value': f'"{value}"'}]

        route53.change_resource_record_sets(
            HostedZoneId=hosted_zone_id,
            ChangeBatch={
                'Changes': [
                    {
                        'Action': 'CREATE',
                        'ResourceRecordSet': {
                            'Name': name,
                            'Type': record_type,
                            'TTL': 300,
                            'ResourceRecords': resource_records
                        }
                    }
                ]
            }
        )
        print(f"Added record {name} with value {value}")
    except Exception as e:
        print(f"Error adding record {name}: {str(e)}")

def main():
    access_key = input("Enter your AWS Access Key: ")
    secret_key = input("Enter your AWS Secret Key: ")
    session_token = input("Enter your AWS Session Token: ")

    try:
        route53 = boto3.client(
            'route53',
            aws_access_key_id=access_key,
            aws_secret_access_key=secret_key,
            aws_session_token=session_token
        )
    except (NoCredentialsError, PartialCredentialsError) as e:
        print(f"Error with credentials: {str(e)}")
        return

    hosted_zones = {
        "cantmessitup.com": "Z04692542SG3KH11U3F46",  # Replace with the actual hosted zone IDs
        "captainscut.com": "Z04197882IGXRBKVKTBL1",
        "fisheryproducts.com": "Z04196933JJJMSO1IWFDR",
        "highlinerculinary.com": "Z0474274388X2SVVCJ6CP",
        "seafoodissmartfood.com": "Z04611673JZ8PTY0Z0WZ",
    }

    records = [
        {
            "domain": "cantmessitup.com",
            "type": "TXT",
            "name": "_dmarc.cantmessitup.com.",
            "value": "v=DMARC1; p=none; rua=mailto:<EMAIL>"
        },
        {
            "domain": "cantmessitup.com",
            "type": "TXT",
            "name": "cantmessitup.com",
            "value": "v=spf1 include:spf.protection.outlook.com include:us._netblocks.mimecast.com -all"
        },
        {
            "domain": "cantmessitup.com",
            "type": "TXT",
            "name": "mimecast20200915._domainkey.cantmessitup.com",
            "value": ("v=DKIM1; k=rsa; p=MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAqY53sZo66fqNpg/FwM3GRgCQ+2pfLtZTnl6ceVhwut6Rm/vwjOTp+XY24qgZ3X+w9Ks257JJNQX2ugER9BtsplTQSgp8+hJzEflIAxvXNuA59f9UJD8YKCNqEz3teUAjYRtzSE0SeiHieIuwePzvhqOody8rYwC5aHADIXY243pC/L8gUFZ7ktmnupW+WUYgfLRiKunQS3QAJCeWjd7HrO0GGrCYe7kMzSJ7z3NBq/47ucjCVp+iydpUU4qfU9DcsvuUAiDraJTs3k9aQFWAimGh4sdKpFylhvYfi6OTlG5sZ/0Ffib/09NCVED88w8+hwxs03DvvWsvsIug5JP/dQIDAQAB")
        },
        {
            "domain": "captainscut.com",
            "type": "TXT",
            "name": "_dmarc.captainscut.com.",
            "value": "v=DMARC1; p=none; rua=mailto:<EMAIL>"
        },
        {
            "domain": "captainscut.com",
            "type": "TXT",
            "name": "captainscut.com",
            "value": "v=spf1 include:spf.protection.outlook.com include:us._netblocks.mimecast.com -all"
        },
        {
            "domain": "captainscut.com",
            "type": "TXT",
            "name": "mimecast20200925._domainkey.captainscut.com",
            "value": ("v=DKIM1; k=rsa; p=MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAyTf53OurPWiQ1+FdVrZjjI7KX1uQp8V/LRujNf+K0b4LHoiC7qvVo/RowkYIfCiRhGe5R8B19o/jYBqpr/UQmMMqJjeFDW86M/pEMOmxfkM9hmA6uxjPAusD0yT1IhnWvFBsk5hF75DP7cruHCkP2BoRDqHIKGxOG9esKowEZhtg7K0rUCbTdaGbkicxJEo/L9LTspi+TUY5IlrtosD9eVKPdPfU860LS8NbnR6qMDyp32AYBPYgAy6EBSJu0VK5a6/Azprvm9iCfvwF78sAvloWR6JcSjRy4eXSDZVDdfg28tsfM3o5I4hCJDNhm/FFru6sh6jNPUR2/KENm5GE9QIDAQAB")
        },
        {
            "domain": "fisheryproducts.com",
            "type": "TXT",
            "name": "_dmarc.fisheryproducts.com.",
            "value": "v=DMARC1; p=none; rua=mailto:<EMAIL>"
        },
        {
            "domain": "fisheryproducts.com",
            "type": "TXT",
            "name": "fisheryproducts.com",
            "value": "v=spf1 include:spf.protection.outlook.com include:us._netblocks.mimecast.com -all"
        },
        {
            "domain": "fisheryproducts.com",
            "type": "TXT",
            "name": "mimecast20200925._domainkey.fisheryproducts.com",
            "value": ("v=DKIM1; k=rsa; p=MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAjfaf+pQW5SlLfl4vK7LmXwtvI/ajXTpKZruIjGx9VMdE3cIf4pjpYnHP7dkNhRsk0kK9021vjkQ6Z00qUhTjqGfCpxap5YKVWs76sRk8JRWhXRUJ3z7M/nVSskW2pVJM9NDAUkddU1fJ8QVa6GOJiQVrQN0uHhu0vD4PHA1sBC8Hnm4aC0R+wEgRObbCl5R4ByNv1LOm26+Sl6tRPsqjIZwxeLJofZecfJFsnmLJPNwPu3HObZzGSgsmnBt4PI19gB3epXtVMbMKLw0+wpZ0T2o+vOSffOxPmwAWeCKag5HgjJ4i/wC/m0QC8xEjG0GQMt05iPTksXvEUpZCADxRjwIDAQAB")
        },
        {
            "domain": "highlinerculinary.com",
            "type": "TXT",
            "name": "_dmarc.highlinerculinary.com.",
            "value": "v=DMARC1; p=none; rua=mailto:<EMAIL>"
        },
        {
            "domain": "highlinerculinary.com",
            "type": "TXT",
            "name": "highlinerculinary.com",
            "value": "v=spf1 include:spf.protection.outlook.com include:us._netblocks.mimecast.com -all"
        },
        {
            "domain": "highlinerculinary.com",
            "type": "TXT",
            "name": "mimecast20200925._domainkey.highlinerculinary.com",
            "value": ("v=DKIM1; k=rsa; p=MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAo6ew990YvEGtj36CZtWXpNoQOvsQ3AvfPLaQHh4FNGNW7EkqPglyEh/V/bGeyU/SSJa+I37ljOerp1JdfmBVGbptHMDIHm5kne1PWg1+NNBNzHTsAdiyJAGWaXuP9/XU5EGGu3afeN85bQbd//+THIrrfAQd9KAQCPH3JVozXqQRylLG9rakqMObCZE+zy7R6yBq+LiXXOdg8Y/P+Vk+zlCLvpx7gX7rLb1yCAM2oYXDY7PrCIGYg3UeDRi0ofGMKk+/JRps576A7n64lN9K6RiOf9Aj3LPU6NlT43peQBh8Z92bDd4tahpQAPpkyR2UXK7tG/S+D1Z/YhGNP6my/QIDAQAB")
        },
        {
            "domain": "seafoodissmartfood.com",
            "type": "TXT",
            "name": "_dmarc.seafoodissmartfood.com.",
            "value": "v=DMARC1; p=none; rua=mailto:<EMAIL>"
        },
        {
            "domain": "seafoodissmartfood.com",
            "type": "TXT",
            "name": "seafoodissmartfood.com",
            "value": "v=spf1 include:spf.protection.outlook.com include:us._netblocks.mimecast.com -all"
        },
        {
            "domain": "seafoodissmartfood.com",
            "type": "TXT",
            "name": "mimecast20200925._domainkey.seafoodissmartfood.com",
            "value": ("v=DKIM1; k=rsa; p=MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAhKa7MGSbTtGUyhxDtMaHx7V8Ki2z6tsynJzgaA1IEerqg1q9aeMAx0QEScjBRsU/iW+HHgd9Cks7BZJBMi1inPcUdSN0joawPH9wgzTAcUdCa86MecMaZJUlflLH0kdeVEQEmXkVbODPs2rpeMfkS0LnXWmNccRTNrtqmDmBk+W5FARQZ2RJ1UYRvMIh4qBLI3S4WGqKeGHxejVHP71ZW47whSYBpJiBE4AnAGW8nrdy7UfSozAyaqHDJNONaMBK7jNih4z42Z/vFNZH7qj65VzFg5sPMfmA1mNXxnCjt+Jmnin4Ro3r5F7oHrGvr+ZPs/eJoKRxQmG1Ru95xv2uRwIDAQAB")
        }
    ]

    for record in records:
        domain = record['domain']
        name = record['name']
        record_type = record['type']
        value = record['value']
        hosted_zone_id = hosted_zones.get(domain)

        if hosted_zone_id:
            if record_exists(route53, hosted_zone_id, name, record_type):
                print(f"Record {name} already exists.")
            else:
                add_record(route53, hosted_zone_id, name, record_type, value)
        else:
            print(f"Hosted zone ID not found for domain {domain}.")

if __name__ == "__main__":
    main()
