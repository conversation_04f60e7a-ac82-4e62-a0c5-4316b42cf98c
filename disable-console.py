import boto3
from botocore.exceptions import ClientError

# Prompt for credentials (all visible)
access_key = input("Enter your AWS Access Key ID: ").strip()
secret_key = input("Enter your AWS Secret Access Key: ").strip()
session_token = input("Enter your AWS Session Token: ").strip()

print("\n[DEBUG] Using the following credentials:")
print(f"AWS Access Key ID: {access_key}")
print(f"AWS Secret Access Key: {secret_key}")
print(f"AWS Session Token: {session_token}\n")

# All service account usernames from the spreadsheet
service_accounts = [
    "MarketingApps", "ParbrinkAPIUser", "SandboxLambdaUser", "admin-tfcreate", "bitbucket",
    "connectria", "corporate-website-email", "cts", "ecr-tscappregistry", "ecr-tscappregistry-poc",
    "eks-tscappregistry", "eks-tscappregistry-poc", "github-actions", "pipeline-s3-user-prod",
    "pipeline-s3-user-qa", "pipeline-s3-user-stage", "s3-pipeline-user", "sandbox-admin-tfcreate",
    "ses-smtp-user.********-115124", "ses-smtp-user.********-111519", "ses-smtp-user.********-104106",
    "snowflake_ingestion", "snowflake_ingestion_vrize", "svc-tscprodecr", "svc-tscprodeks",
    "svcdatapod", "terraform_user", "tscup2-deployer", "tscup2-ses-smtp-user", "wordpress",
    "wordpress-static-generator"
]

# Initialize IAM client
iam = boto3.client(
    'iam',
    aws_access_key_id=access_key,
    aws_secret_access_key=secret_key,
    aws_session_token=session_token
)

# Disable console access if enabled
for user in service_accounts:
    try:
        iam.get_login_profile(UserName=user)
        print(f"[INFO] Console access is ENABLED for '{user}'. Disabling it...")
        iam.delete_login_profile(UserName=user)
        print(f"[SUCCESS] Console access DISABLED for '{user}'.")
    except iam.exceptions.NoSuchEntityException:
        print(f"[OK] Console access is already DISABLED for '{user}'.")
    except ClientError as e:
        print(f"[ERROR] Unexpected error for '{user}': {e}")
