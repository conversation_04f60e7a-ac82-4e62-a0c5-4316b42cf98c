import boto3
import json

# Function to fetch EBS volume data
def get_ebs_volume_data(access_key, secret_key, session_token, region='us-east-1'):
    session = boto3.Session(
        aws_access_key_id=access_key,
        aws_secret_access_key=secret_key,
        aws_session_token=session_token,
        region_name=region
    )
    
    ec2_client = session.client('ec2')

    instances = ec2_client.describe_instances()
    
    volume_data = []

    for reservation in instances['Reservations']:
        for instance in reservation['Instances']:
            instance_id = instance['InstanceId']
            volumes = ec2_client.describe_volumes(
                Filters=[{'Name': 'attachment.instance-id', 'Values': [instance_id]}]
            )
            
            for volume in volumes['Volumes']:
                volume_info = {
                    'InstanceId': instance_id,
                    'VolumeId': volume['VolumeId'],
                    'Size (GiB)': volume['Size'],
                    'State': volume['State'],
                    'AvailabilityZone': volume['AvailabilityZone'],
                    'Type': volume['VolumeType']
                }
                volume_data.append(volume_info)
    
    return volume_data


if __name__ == "__main__":
    access_key = input("Enter your AWS Access Key: ")
    secret_key = input("Enter your AWS Secret Key: ")
    session_token = input("Enter your AWS Session Token: ")
    region = input("Enter AWS Region (default: us-east-1): ") or "us-east-1"

    data = get_ebs_volume_data(access_key, secret_key, session_token, region)

    print("\nEBS Volume Data:")
    print(json.dumps(data, indent=4))
