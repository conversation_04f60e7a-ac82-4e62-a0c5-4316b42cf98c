import boto3
from botocore.exceptions import NoCredentialsError, PartialCredentialsError

# List of volume IDs to be deleted
volumes_to_delete = [
    "vol-0f4080760f24187cf", "vol-0fb94615ccb5a89fa", "vol-0e443f372f149d2d0",
    "vol-089abc726d4ec22ad", "vol-093324d52b81536b8", "vol-061aceb4aa885603e",
    "vol-055df9eda7627646b", "vol-0d7f1e4e198b5baf5", "vol-0941626b17ca5aa3a",
    "vol-05eca73140b8d27fa", "vol-0a78bc1c266bbf55a", "vol-08bd1737641dd771d",
    "vol-09297510ee8950cb1", "vol-04c7de2dbb77b0165", "vol-09a3699fdb968e534"
]

def delete_volumes(volumes, region):
    # Prompt user for AWS credentials
    aws_access_key_id = input("Enter your AWS Access Key ID: ")
    aws_secret_access_key = input("Enter your AWS Secret Access Key: ")
    aws_session_token = input("Enter your AWS Session Token (if applicable, press Enter to skip): ")

    try:
        # Create a boto3 session with the provided credentials
        session = boto3.Session(
            aws_access_key_id=aws_access_key_id,
            aws_secret_access_key=aws_secret_access_key,
            aws_session_token=aws_session_token,
            region_name=region
        )

        # Create an EC2 resource
        ec2 = session.resource('ec2')

        # Loop through and delete each volume
        for volume_id in volumes:
            volume = ec2.Volume(volume_id)
            try:
                print(f"Deleting volume {volume_id}...")
                volume.delete()
                print(f"Volume {volume_id} deleted successfully.")
            except Exception as e:
                print(f"Failed to delete volume {volume_id}: {e}")
    except (NoCredentialsError, PartialCredentialsError):
        print("Error: AWS credentials not provided or incomplete.")

if __name__ == "__main__":
    delete_volumes(volumes_to_delete, 'us-east-1')
